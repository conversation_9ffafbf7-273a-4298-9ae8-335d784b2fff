import React from 'react';
import GalleryImage from './GalleryImage';

const galleryItems = [
  {
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuA7w9N14rC0kpL-oKDNdXB2RBbbEt3bkkAiMUw90ilcNsio4NdErcvCW2QA-2GTo0gF6ll45gnFUEm0ISiGtfBJx_FPWaV_pyVQ9fjaqYtI1sbS0ZF9Nq_YQSudhoq1FFZf9ewMQSMDgJce2ILmSFKUuK-DSJdyJagpWJ46zn2WDJ1KDRBGK2CjR-jqKnRodrSmUDsppCbirxG8BzR1IL58nc1_FUpSTkfR9fHkON8tK6Zgu5alEcsQmsrPtSY-iEhJxTNkwrorqKM",
    altText: "Intricate Sleeve Tattoo",
    title: "Intricate Sleeve"
  },
  {
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuDmO4UzeLwRqnpPHEywfLlbQjI-XErVLbGzKV9BYT6nMOK6CbR97DAIZ9EYSP058Bgs6lkURGCEDbugnFguF2ppz-UdBULBi8KMNHU3kSfV5t2hkhP1saKh7O-VmQL0-CuXUVlIjQsZ9rIcmBSbnKmYeS38v7DK4UsF83wZqkrt01TXoWVG5SXe8ojYNaKFD9BsRgecHdbxZ3Pce8K-TpPAUfr_ohPxYU5Zur8ZaCivk0fm1LtUZk5njCAnlNb3_fiv6CoKvN_uomc",
    altText: "Minimalist Design Tattoo",
    title: "Minimalist Design"
  },
  {
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuBYp0Rf59CKZXD6rv1nbRM2U1GISnBkjBwO6VVgeaUdC3J0DPd9g0knuc_3mQ7NWvRQQ9bl_ntCoKU-3begahO2hHGqmlaED5FN6jUl0b99KNf-ceQ9Vexta2ZxYP2SJallJ5OKGRGl55j7-VYRbIVDh8JuPnOB-siSgOz_kbNeIILSCRjZga0DFYuws7fDmWXlAdsvrAIPJ4Hy4BIwYsbGdtzx2NrGdt7BC-rh3KkWV9mEKLbpSMmgJPQvk3d7giJNCQuXbNty-eQ",
    altText: "Vibrant Color Tattoo",
    title: "Vibrant Color"
  }
];

const GallerySection = () => {
  return (
    <section className="py-16 sm:py-24 bg-gray-900/50" id="gallery">
      <div className="container mx-auto px-4">
        <h2 className="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
          Galeria
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {galleryItems.map((item) => (
            <GalleryImage
              key={item.title}
              imageUrl={item.imageUrl}
              altText={item.altText}
              title={item.title}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default GallerySection;
