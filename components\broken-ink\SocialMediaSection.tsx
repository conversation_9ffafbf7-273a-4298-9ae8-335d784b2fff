import React from "react";
import SocialCard from "./SocialCard";
import { getIconComponent } from "@/lib/iconUtils";

const socialPlatforms = [
  {
    iconName: "instagram",
    platformName: "Instagram",
    description: "Veja nosso portfólio e atualizações diárias.",
    href: "#",
  },
  {
    iconName: "facebook",
    platformName: "Facebook",
    description: "Participe da nossa comunidade e eventos.",
    href: "#",
  },
  {
    iconName: "twitter",
    platformName: "Twitter",
    description: "Notícias rápidas e novidades do estúdio.",
    href: "#",
  },
  {
    iconName: "tiktok",
    platformName: "TikTok",
    description: "Notícias rápidas e novidades do estúdio.",
    href: "#",
  },
  {
    iconName: "pinterest",
    platformName: "Pinterest",
    description: "Notícias rápidas e novidades do estúdio.",
    href: "#",
  },
  {
    iconName: "globe",
    platformName: "Tattoo.com",
    description: "Nosso perfil oficial na indústria.",
    href: "#",
  },
];

const SocialMediaSection = () => {
  return (
    <section className="py-16 bg-black sm:py-20">
      <div className="container mx-auto px-4 space-y-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Nos Siga nas Redes Sociais
          </h2>
          <p className="mt-4 text-lg leading-8 text-gray-300">
            Acompanhe nossa jornada e inspire-se com nossos trabalhos mais
            recentes.
          </p>
        </div>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {socialPlatforms.map((platform) => {
            const IconComponent = getIconComponent(platform.iconName);
            return (
              <SocialCard
                key={platform.platformName}
                icon={<IconComponent className="h-6 w-6 text-white" />}
                platformName={platform.platformName}
                description={platform.description}
                href={platform.href}
              />
            );
          })}
        </div>
        <div className="flex justify-center">
          <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-8 bg-white text-black text-base font-bold leading-normal tracking-wide transition-transform hover:scale-105">
            <span className="truncate">Ver Todas as Plataformas</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default SocialMediaSection;
