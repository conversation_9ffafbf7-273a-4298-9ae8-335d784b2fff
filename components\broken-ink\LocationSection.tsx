import React from 'react';

const LocationSection = () => {
  return (
    <section className="py-16 sm:py-24" id="location">
      <div className="container mx-auto px-4">
        <h2 className="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
          Localização
        </h2>
        <div className="aspect-video w-full rounded-2xl overflow-hidden shadow-lg">
          {/* Placeholder for map integration.
              For example, Google Maps iframe or a React map library.
              A simple placeholder text or image could also go here.
          */}
          <div className="bg-gray-700 w-full h-full flex items-center justify-center text-white">
            [Map Placeholder]
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationSection;
