import React from "react";
import ServiceCard from "./ServiceCard";
import { getIconComponent } from "@/lib/iconUtils";

const services = [
  {
    iconName: "brush",
    title: "Tatuagens Personalizadas",
    description:
      "Dê vida à sua visão com uma peça única desenhada por nossos talentosos artistas.",
  },
  {
    iconName: "palette",
    title: "Designs Prontos",
    description:
      "Escolha entre nossa coleção curada de designs pré-desenhados, prontos para serem tatuados.",
  },
  {
    iconName: "refresh",
    title: "Cover-Ups",
    description:
      "Expertly conceal or transform old tattoos with our creative cover-up solutions.",
  },
];

const ServicesSection = () => {
  return (
    <section className="py-16 sm:py-24" id="services">
      <div className="container mx-auto px-4">
        <h2 className="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
          Nossos Serviços
        </h2>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {services.map((service) => {
            const IconComponent = getIconComponent(service.iconName);
            return (
              <ServiceCard
                key={service.title}
                icon={<IconComponent className="h-8 w-8" />}
                title={service.title}
                description={service.description}
              />
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
