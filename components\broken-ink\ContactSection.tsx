import React from "react";
import { getIconComponent } from "@/lib/iconUtils";

const socialLinks = [
  {
    iconName: "instagram",
    label: "Instagram",
    href: "#",
  },
  {
    iconName: "facebook",
    label: "Facebook",
    href: "#",
  },
  {
    iconName: "twitter",
    label: "Twitter",
    href: "#",
  },
];

const ContactSection = () => {
  return (
    <section className="py-16 sm:py-24 bg-gray-900/50" id="contact">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-6">
          Broken Ink Tattoo
        </h2>
        <div className="text-gray-300 text-lg space-y-2">
          <p>
            Email:{" "}
            <a
              className="hover:text-white transition-colors"
              href="mailto:<EMAIL>"
            >
              <EMAIL>
            </a>
          </p>
          <p>
            Phone:{" "}
            <a
              className="hover:text-white transition-colors"
              href="tel:XX99901XXXX"
            >
              (XX) 99901-XXXX
            </a>
          </p>
          <p>Inkwell Street, 123 | São Paulo, SP</p>
        </div>
        <div className="mt-12">
          <h3 className="text-white text-2xl font-bold mb-6">Nossas redes</h3>
          <div className="flex justify-center gap-6">
            {socialLinks.map((social) => {
              const IconComponent = getIconComponent(social.iconName);
              return (
                <a
                  key={social.label}
                  className="text-gray-400 hover:text-white transition-colors"
                  href={social.href}
                  aria-label={social.label}
                >
                  <IconComponent className="h-7 w-7" />
                </a>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
