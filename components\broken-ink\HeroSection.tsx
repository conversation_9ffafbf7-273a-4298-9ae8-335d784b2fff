import React from "react";

const HeroSection = () => {
  return (
    <section className="relative">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url("https://scontent.fbfh8-1.fna.fbcdn.net/v/t39.30808-6/476456513_937782661848653_6745617227350114204_n.jpg?_nc_cat=100&ccb=1-7&_nc_sid=cc71e4&_nc_ohc=e7sXJtmLNP4Q7kNvwELDazQ&_nc_oc=Admx34j2Tf_YoMuPLqX4cJ8MEZaKHVEPO8uO7wr5aK1Q4bHEKyVcyaKUHfWLjRMF0wF9EfFWOjZe-S1tcQ8sd6ns&_nc_zt=23&_nc_ht=scontent.fbfh8-1.fna&_nc_gid=x1sWburtgv1RfG-25TsNyQ&oh=00_AfR673nWeKRtmpW0wm8V5vX-UbAnjUoUvMtXdYQP9vAPOA&oe=68739539")`,
        }}
      ></div>
      {/* Aggressive gradient overlay that fades from light to very dark at bottom */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/60 to-black/95"></div>
      {/* Strong bottom gradient for seamless blending with black component */}
      <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-b from-transparent via-black/40 to-black"></div>
      <div className="relative container mx-auto flex min-h-[70vh] flex-col items-center justify-center gap-6 p-4 pb-16 text-center">
        <h1 className="text-white text-5xl font-black leading-tight tracking-[-0.033em] md:text-7xl">
          Broken Ink Tattoo
        </h1>
        <p className="max-w-xl text-lg font-light text-gray-200 md:text-xl">
          Onde a arte encontra a pele. Explore nossos designs únicos e artistas
          talentosos.
        </p>
        <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-6 bg-white text-black text-base font-bold leading-normal tracking-wide transition-transform hover:scale-105">
          <span className="truncate">Agendar</span>
        </button>
      </div>
    </section>
  );
};

export default HeroSection;
