import React from 'react';
import ArtistProfile from './ArtistProfile';

const artists = [
  {
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuD276eMEwr2iOqz4mmHWMuFUqDz7Fg5g-aXV5ePnr8XEPoE7SAcYMGhs2Q2JJHonU1zu12FN7-78c24bBLsyhVjlw2rLHtHzNlfi6yK5We_wRKlAHcFI1d0xxcrhy35KaYvxwpGGmG4TNbVTsvl4zp3x5WIKF3u407AqtvZhE_dLwT5afq8bNDCBr4i1SgvIZGRrQn_5Y_ERKS0JmWN_9S_tEJ5jr9o02sUwtDaWjDZUKmkRwSuOB-mBnbT3ONiS8JLzIpQDijmSZg",
    name: "<PERSON>",
    specialty: "Specializes in realism"
  },
  {
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuCecqLAEOZJpFOXfof9zq8_LvxvCNJYHk9e1yCclunEljJw3uPyUEQK38XP2WcJCVFH20zzIg3Dz3jwGuDwu9xKFQC8IC2YK-3oRySz10BEc1feSuBsN4tbKR4PbM7-4Do1ZzCvK8Bz3UKe1_tuwziUifNZFJMR0tvwYraMP0rI7kRXrFNK7Gib2M77XsFq0c7iYJaMp000RzorawA_vfznbL16GDN9jOo_JxEceYN9yttyDky232CU49YHq5z-XzConRPIaNE5HyI",
    name: "Olivia Hayes",
    specialty: "Known for fine lines"
  },
  {
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuBtBwtmwgBupr4XTr_AMJGYLuN5ZIY4QPSheoJzIXV4byBVriNlVcr0A1uOB23gLFidwLetSlsJ_KBfi51795LPaLaZ-MpDwp0pUnytE71it2KVFKfLu6t49Ff__ZwAEmcmkQp-7xHUxR4f3xqBRC9tvPt8KY9RrrpFjsBZB83p-jLpxbQrmx7PzC3jmqs0HA16v5ETW5MdS7UnRC3iy6Y5wdwfYKN-J5-pxxUkDCSBgafhkvNBVtMmpGd7sYtgvJuUqrc3wm82h2E",
    name: "Noah Bennett",
    specialty: "Expert in traditional"
  }
];

const ArtistsSection = () => {
  return (
    <section className="py-16 sm:py-24" id="artists">
      <div className="container mx-auto px-4">
        <h2 className="text-center text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-12">
          Nossos Artistas
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 text-center">
          {artists.map((artist) => (
            <ArtistProfile
              key={artist.name}
              imageUrl={artist.imageUrl}
              name={artist.name}
              specialty={artist.specialty}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ArtistsSection;
