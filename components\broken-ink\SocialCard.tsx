import React from 'react';

interface SocialCardProps {
  icon: React.ReactNode;
  platformName: string;
  description: string;
  href?: string;
}

const SocialCard: React.FC<SocialCardProps> = ({ icon, platformName, description, href = "#" }) => {
  return (
    <a
      className="group relative flex flex-col justify-between rounded-2xl bg-gray-900/50 p-6 ring-1 ring-white/10 transition-all hover:ring-white/20"
      href={href}
    >
      <div className="flex items-center gap-4">
        <div className="bg-gray-800 p-3 rounded-lg">
          {icon}
        </div>
        <p className="text-lg font-semibold text-white">{platformName}</p>
      </div>
      <p className="mt-4 text-gray-400">{description}</p>
    </a>
  );
};

export default SocialCard;
